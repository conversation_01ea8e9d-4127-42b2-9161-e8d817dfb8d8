# SkillSwap Application Startup Script
Write-Host "=== Starting SkillSwap Application ===" -ForegroundColor Green

# Function to start a service in a new PowerShell window
function Start-ServiceWindow {
    param(
        [string]$Title,
        [string]$Command,
        [string]$WorkingDirectory = $PWD
    )
    
    $arguments = @(
        "-NoExit"
        "-Command"
        "cd '$WorkingDirectory'; Write-Host 'Starting $Title...' -ForegroundColor Green; $Command"
    )
    
    Start-Process -FilePath "powershell.exe" -ArgumentList $arguments -WindowStyle Normal
    Write-Host "✓ Started $Title in new window" -ForegroundColor Green
}

# Check if Node.js dependencies are installed
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing Node.js dependencies..." -ForegroundColor Yellow
    npm install
}

# Check if Python virtual environment exists
if (-not (Test-Path "ml_service\venv")) {
    Write-Host "Setting up Python virtual environment..." -ForegroundColor Yellow
    cd ml_service
    python -m venv venv
    .\venv\Scripts\python.exe -m pip install -r requirements.txt
    cd ..
}

Write-Host "`nStarting all services..." -ForegroundColor Yellow

# Start Mock PHP Backend Server
Start-ServiceWindow "PHP Backend (Mock)" "node php-server.js" $PWD

# Wait a moment
Start-Sleep -Seconds 2

# Start Python ML Service
Start-ServiceWindow "ML Service" ".\venv\Scripts\python.exe -m uvicorn main:app --reload --port 8000" "$PWD\ml_service"

# Wait a moment
Start-Sleep -Seconds 3

# Start Next.js Frontend
Start-ServiceWindow "Frontend" "npm run dev" $PWD

Write-Host "`n=== All Services Started! ===" -ForegroundColor Green
Write-Host "Services are running in separate windows:" -ForegroundColor Yellow
Write-Host "• Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "• Mock PHP Backend: http://localhost:8080" -ForegroundColor Cyan
Write-Host "• ML Service: http://localhost:8000" -ForegroundColor Cyan

Write-Host "`nWaiting 10 seconds before opening browser..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Open the application in browser
Write-Host "Opening application in browser..." -ForegroundColor Green
Start-Process "http://localhost:3000"

Write-Host "`n=== Setup Complete! ===" -ForegroundColor Green
Write-Host "The application should now be running!" -ForegroundColor Green
Write-Host "Check the separate PowerShell windows for service logs." -ForegroundColor Yellow

# Keep this window open
Write-Host "`nPress any key to exit this setup window..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
