const express = require('express');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const cors = require('cors');

const app = express();
const PORT = 8080;

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:8000'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Mock PHP backend responses since we don't have PHP installed
const mockResponses = {
  '/skillswap-api/auth.php': (req, res) => {
    const { action, email, password } = req.body;
    
    if (action === 'login') {
      // Mock login response
      if (email && password) {
        res.json({
          success: true,
          token: 'mock-jwt-token-' + Date.now(),
          user: {
            id: 1,
            username: email.split('@')[0],
            email: email,
            full_name: 'Test User'
          },
          message: 'Login successful'
        });
      } else {
        res.json({
          success: false,
          message: 'Invalid credentials'
        });
      }
    } else if (action === 'register') {
      res.json({
        success: true,
        message: 'Registration successful',
        user: {
          id: Date.now(),
          username: req.body.username,
          email: req.body.email
        }
      });
    } else {
      res.json({ success: false, message: 'Invalid action' });
    }
  },

  '/skillswap-api/skills.php': (req, res) => {
    const mockSkills = [
      { id: 1, name: 'JavaScript', category: 'Programming' },
      { id: 2, name: 'Python', category: 'Programming' },
      { id: 3, name: 'React', category: 'Web Development' },
      { id: 4, name: 'Node.js', category: 'Backend Development' },
      { id: 5, name: 'Machine Learning', category: 'Data Science' },
      { id: 6, name: 'Graphic Design', category: 'Design' },
      { id: 7, name: 'Digital Marketing', category: 'Marketing' },
      { id: 8, name: 'Photography', category: 'Creative' }
    ];
    
    res.json({
      success: true,
      data: mockSkills
    });
  },

  '/skillswap-api/search.php': (req, res) => {
    const mockUsers = [
      {
        id: 1,
        username: 'john_doe',
        full_name: 'John Doe',
        bio: 'Full-stack developer passionate about teaching',
        skills: ['JavaScript', 'React', 'Node.js'],
        location: 'New York, NY'
      },
      {
        id: 2,
        username: 'jane_smith',
        full_name: 'Jane Smith',
        bio: 'Data scientist and ML enthusiast',
        skills: ['Python', 'Machine Learning', 'Data Analysis'],
        location: 'San Francisco, CA'
      }
    ];
    
    res.json({
      success: true,
      data: mockUsers
    });
  },

  '/skillswap-api/messages.php': (req, res) => {
    const mockMessages = [
      {
        id: 1,
        sender_id: 2,
        sender_name: 'Jane Smith',
        message: 'Hi! I saw you\'re teaching JavaScript. I\'d love to learn!',
        created_at: new Date().toISOString()
      }
    ];
    
    res.json({
      success: true,
      data: mockMessages
    });
  },

  '/skillswap-api/reels.php': (req, res) => {
    const mockReels = [
      {
        id: 1,
        user_id: 1,
        title: 'JavaScript Fundamentals',
        description: 'Learn the basics of JavaScript',
        thumbnail_url: '/placeholder.jpg',
        video_url: '#',
        likes_count: 25,
        views_count: 150,
        skill: 'JavaScript'
      },
      {
        id: 2,
        user_id: 2,
        title: 'Python for Beginners',
        description: 'Getting started with Python programming',
        thumbnail_url: '/placeholder.jpg',
        video_url: '#',
        likes_count: 18,
        views_count: 89,
        skill: 'Python'
      }
    ];
    
    res.json({
      success: true,
      data: mockReels
    });
  },

  '/skillswap-api/test_db.php': (req, res) => {
    res.json({
      success: true,
      message: 'Mock database connection successful',
      timestamp: new Date().toISOString()
    });
  }
};

// Handle all skillswap-api routes
app.all('/skillswap-api/:endpoint', (req, res) => {
  const route = req.path;

  console.log(`${req.method} ${route}`, req.body);

  if (mockResponses[route]) {
    mockResponses[route](req, res);
  } else {
    res.json({
      success: false,
      message: `Route ${route} not implemented in mock server`
    });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Mock PHP server is running',
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`Mock PHP server running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('- POST /skillswap-api/auth.php');
  console.log('- GET /skillswap-api/skills.php');
  console.log('- GET /skillswap-api/search.php');
  console.log('- GET /skillswap-api/messages.php');
  console.log('- GET /skillswap-api/reels.php');
  console.log('- GET /skillswap-api/test_db.php');
  console.log('- GET /health');
});

module.exports = app;
